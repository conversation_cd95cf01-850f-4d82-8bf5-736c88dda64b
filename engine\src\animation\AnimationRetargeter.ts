/**
 * 动画重定向器
 * 用于将一个骨骼结构的动画应用到另一个骨骼结构上
 */
import * as THREE from 'three';
import type { AnimationClip } from './AnimationClip';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 骨骼映射
 */
export interface BoneMapping {
  /** 源骨骼名称 */
  source: string;
  /** 目标骨骼名称 */
  target: string;
  /** 旋转偏移 */
  rotationOffset?: THREE.Quaternion;
  /** 位置缩放 */
  positionScale?: number;
  /** 是否镜像 */
  mirror?: boolean;
}

/**
 * 重定向配置
 */
export interface RetargetConfig {
  /** 骨骼映射 */
  boneMapping: BoneMapping[];
  /** 是否保留位置轨道 */
  preservePositionTracks?: boolean;
  /** 是否保留缩放轨道 */
  preserveScaleTracks?: boolean;
  /** 是否规范化旋转 */
  normalizeRotations?: boolean;
  /** 是否调整根骨骼高度 */
  adjustRootHeight?: boolean;
  /** 是否调整骨骼长度 */
  adjustBoneLength?: boolean;
  /** 是否使用四元数球面线性插值 */
  useQuaternionSlerp?: boolean;
  /** 是否自动创建骨骼映射 */
  autoCreateMapping?: boolean;
  /** 是否忽略未映射的骨骼 */
  ignoreUnmappedBones?: boolean;
  /** 是否启用T-Pose检测 */
  enableTPoseDetection?: boolean;
  /** 是否启用IK约束 */
  enableIKConstraints?: boolean;
  /** 是否启用面部动画重定向 */
  enableFacialRetargeting?: boolean;
  /** 是否启用手指动画重定向 */
  enableFingerRetargeting?: boolean;
  /** 是否启用质量评估 */
  enableQualityAssessment?: boolean;
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 重定向模式 */
  retargetingMode?: 'skeleton' | 'proportional' | 'absolute';
  /** 过滤设置 */
  filtering?: {
    positionFilter?: number;
    rotationFilter?: number;
    scaleFilter?: number;
  };
}

/**
 * IK约束配置
 */
export interface IKConstraint {
  /** 约束类型 */
  type: 'two-bone' | 'multi-bone' | 'look-at';
  /** 目标骨骼 */
  targetBone: string;
  /** 约束骨骼链 */
  boneChain: string[];
  /** 权重 */
  weight: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 重定向质量评估
 */
export interface RetargetQualityAssessment {
  /** 总体质量分数 (0-1) */
  overallScore: number;
  /** 骨骼映射质量 */
  boneMappingQuality: number;
  /** 比例一致性 */
  proportionConsistency: number;
  /** 动画保真度 */
  animationFidelity: number;
  /** 详细报告 */
  detailedReport: {
    unmappedBones: string[];
    scaleMismatches: { bone: string; ratio: number }[];
    rotationErrors: { bone: string; error: number }[];
    warnings: string[];
  };
}

/**
 * 重定向事件类型
 */
export enum RetargetEventType {
  /** 重定向开始 */
  RETARGET_START = 'retargetStart',
  /** 重定向完成 */
  RETARGET_COMPLETE = 'retargetComplete',
  /** 重定向错误 */
  RETARGET_ERROR = 'retargetError',
  /** T-Pose检测完成 */
  TPOSE_DETECTED = 'tPoseDetected',
  /** 质量评估完成 */
  QUALITY_ASSESSED = 'qualityAssessed'
}

/**
 * 动画重定向器
 */
export class AnimationRetargeter {
  /** 源骨骼 */
  private sourceSkeleton: THREE.Skeleton | THREE.Bone[];
  /** 目标骨骼 */
  private targetSkeleton: THREE.Skeleton | THREE.Bone[];
  /** 重定向配置 */
  private config: RetargetConfig;
  /** 骨骼映射缓存 */
  private boneMappingCache: Map<string, string> = new Map();
  /** 骨骼索引映射缓存 */
  private boneIndexMappingCache: Map<number, number> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 骨骼层次结构缓存 */
  private boneHierarchyCache: Map<string, string[]> = new Map();
  /** 骨骼长度缓存 */
  private boneLengthCache: Map<string, number> = new Map();
  /** T-Pose数据缓存 */
  private tPoseCache: Map<string, THREE.Quaternion> = new Map();
  /** 重定向结果缓存 */
  private retargetCache: Map<string, THREE.AnimationClip> = new Map();
  /** IK约束配置 */
  private ikConstraints: Map<string, IKConstraint> = new Map();
  /** 质量评估结果 */
  private qualityAssessment: RetargetQualityAssessment | null = null;

  /**
   * 构造函数
   * @param sourceSkeleton 源骨骼
   * @param targetSkeleton 目标骨骼
   * @param config 重定向配置
   */
  constructor(
    sourceSkeleton: THREE.Skeleton | THREE.Bone[],
    targetSkeleton: THREE.Skeleton | THREE.Bone[],
    config: RetargetConfig
  ) {
    this.sourceSkeleton = sourceSkeleton;
    this.targetSkeleton = targetSkeleton;
    this.config = {
      boneMapping: [...config.boneMapping],
      preservePositionTracks: config.preservePositionTracks !== undefined ? config.preservePositionTracks : true,
      preserveScaleTracks: config.preserveScaleTracks !== undefined ? config.preserveScaleTracks : false,
      normalizeRotations: config.normalizeRotations !== undefined ? config.normalizeRotations : true,
      adjustRootHeight: config.adjustRootHeight !== undefined ? config.adjustRootHeight : true,
      adjustBoneLength: config.adjustBoneLength !== undefined ? config.adjustBoneLength : true,
      useQuaternionSlerp: config.useQuaternionSlerp !== undefined ? config.useQuaternionSlerp : true,
      autoCreateMapping: config.autoCreateMapping !== undefined ? config.autoCreateMapping : false,
      ignoreUnmappedBones: config.ignoreUnmappedBones !== undefined ? config.ignoreUnmappedBones : true,
      enableTPoseDetection: config.enableTPoseDetection !== undefined ? config.enableTPoseDetection : false,
      enableIKConstraints: config.enableIKConstraints !== undefined ? config.enableIKConstraints : false,
      enableFacialRetargeting: config.enableFacialRetargeting !== undefined ? config.enableFacialRetargeting : false,
      enableFingerRetargeting: config.enableFingerRetargeting !== undefined ? config.enableFingerRetargeting : false,
      enableQualityAssessment: config.enableQualityAssessment !== undefined ? config.enableQualityAssessment : true,
      enableCache: config.enableCache !== undefined ? config.enableCache : true,
      retargetingMode: config.retargetingMode || 'skeleton',
      filtering: config.filtering || {
        positionFilter: 0.1,
        rotationFilter: 0.05,
        scaleFilter: 0.02
      }
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    // 如果已初始化，则不再初始化
    if (this.initialized) return;

    // 创建骨骼映射缓存
    this.createBoneMappingCache();

    // 构建骨骼层次结构缓存
    this.buildBoneHierarchyCache();

    // 如果启用自动创建映射，则尝试自动创建
    if (this.config.autoCreateMapping) {
      this.autoCreateBoneMapping();
    }

    // 如果启用T-Pose检测，检测源和目标骨骼的T-Pose
    if (this.config.enableTPoseDetection) {
      this.detectTPose(this.sourceSkeleton);
      this.detectTPose(this.targetSkeleton);
    }

    // 标记为已初始化
    this.initialized = true;
  }

  /**
   * 创建骨骼映射缓存
   */
  private createBoneMappingCache(): void {
    // 清空缓存
    this.boneMappingCache.clear();
    this.boneIndexMappingCache.clear();

    // 获取骨骼数组
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();

    // 创建骨骼名称映射
    for (const mapping of this.config.boneMapping) {
      this.boneMappingCache.set(mapping.source, mapping.target);
    }

    // 创建骨骼索引映射
    for (let i = 0; i < sourceBones.length; i++) {
      const sourceBone = sourceBones[i];
      const targetBoneName = this.boneMappingCache.get(sourceBone.name);

      if (targetBoneName) {
        // 查找目标骨骼索引
        const targetIndex = targetBones.findIndex(bone => bone.name === targetBoneName);
        if (targetIndex !== -1) {
          this.boneIndexMappingCache.set(i, targetIndex);
        }
      }
    }
  }

  /**
   * 自动创建骨骼映射
   */
  public autoCreateBoneMapping(): void {
    // 获取骨骼数组
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();

    // 尝试匹配相同名称的骨骼
    for (const sourceBone of sourceBones) {
      // 如果已经有映射，则跳过
      if (this.boneMappingCache.has(sourceBone.name)) continue;

      // 查找相同名称的目标骨骼
      const targetBone = targetBones.find(bone => bone.name === sourceBone.name);
      if (targetBone) {
        // 添加映射
        this.config.boneMapping.push({
          source: sourceBone.name,
          target: targetBone.name
        });
        this.boneMappingCache.set(sourceBone.name, targetBone.name);
      }
    }

    // 更新骨骼索引映射
    this.createBoneMappingCache();
  }

  /**
   * 获取源骨骼数组
   * @returns 骨骼数组
   */
  private getSourceBones(): THREE.Bone[] {
    if (Array.isArray(this.sourceSkeleton)) {
      return this.sourceSkeleton;
    } else {
      return this.sourceSkeleton.bones;
    }
  }

  /**
   * 获取目标骨骼数组
   * @returns 骨骼数组
   */
  private getTargetBones(): THREE.Bone[] {
    if (Array.isArray(this.targetSkeleton)) {
      return this.targetSkeleton;
    } else {
      return this.targetSkeleton.bones;
    }
  }

  /**
   * 重定向动画片段
   * @param clip 动画片段
   * @returns 重定向后的动画片段
   */
  public retarget(clip: THREE.AnimationClip): THREE.AnimationClip {
    // 发出重定向开始事件
    this.eventEmitter.emit(RetargetEventType.RETARGET_START, {
      clip: clip.name
    });

    try {
      // 创建新轨道
      const newTracks: THREE.KeyframeTrack[] = [];

      // 处理每个轨道
      for (const track of clip.tracks) {
        // 解析轨道名称
        const trackSplits = track.name.split('.');
        const boneName = trackSplits[0];
        const property = trackSplits[1];

        // 获取目标骨骼名称
        const targetBoneName = this.boneMappingCache.get(boneName);
        if (!targetBoneName && this.config.ignoreUnmappedBones) {
          continue;
        }

        // 创建新轨道
        let newTrack: THREE.KeyframeTrack = null;

        if (property === 'quaternion') {
          // 处理旋转轨道
          newTrack = this.retargetRotationTrack(track as THREE.QuaternionKeyframeTrack, boneName, targetBoneName);
        } else if (property === 'position' && this.config.preservePositionTracks) {
          // 处理位置轨道
          newTrack = this.retargetPositionTrack(track as THREE.VectorKeyframeTrack, boneName, targetBoneName);
        } else if (property === 'scale' && this.config.preserveScaleTracks) {
          // 处理缩放轨道
          newTrack = this.retargetScaleTrack(track as THREE.VectorKeyframeTrack, boneName, targetBoneName);
        }

        // 添加新轨道
        if (newTrack) {
          newTracks.push(newTrack);
        }
      }

      // 创建新的动画片段
      const newClip = new THREE.AnimationClip(
        clip.name,
        clip.duration,
        newTracks,
        clip.blendMode
      );

      // 发出重定向完成事件
      this.eventEmitter.emit(RetargetEventType.RETARGET_COMPLETE, {
        clip: clip.name,
        newClip: newClip.name
      });

      return newClip;
    } catch (error) {
      // 发出重定向错误事件
      this.eventEmitter.emit(RetargetEventType.RETARGET_ERROR, {
        clip: clip.name,
        error
      });

      throw error;
    }
  }

  /**
   * 重定向旋转轨道
   * @param track 旋转轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @returns 重定向后的旋转轨道
   */
  private retargetRotationTrack(
    track: THREE.QuaternionKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string
  ): THREE.QuaternionKeyframeTrack {
    // 复制时间和值
    const times = track.times.slice();
    const values = track.values.slice();

    // 获取骨骼映射
    const mapping = this.config.boneMapping.find(m => m.source === sourceBoneName);
    if (!mapping) return null;

    // 如果有旋转偏移，则应用旋转偏移
    if (mapping.rotationOffset) {
      const rotationOffset = mapping.rotationOffset.clone();

      // 处理每一帧
      for (let i = 0; i < track.times.length; i++) {
        const quaternion = new THREE.Quaternion();
        quaternion.fromArray(track.values, i * 4);

        // 应用旋转偏移
        if (mapping.mirror) {
          // 镜像旋转
          quaternion.y *= -1;
          quaternion.z *= -1;
        }

        // 应用旋转偏移
        quaternion.premultiply(rotationOffset);

        // 规范化旋转
        if (this.config.normalizeRotations) {
          quaternion.normalize();
        }

        // 存储结果
        quaternion.toArray(values, i * 4);
      }
    }

    // 创建新轨道
    return new THREE.QuaternionKeyframeTrack(
      `${targetBoneName}.quaternion`,
      times,
      values
    );
  }

  /**
   * 重定向位置轨道
   * @param track 位置轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @returns 重定向后的位置轨道
   */
  private retargetPositionTrack(
    track: THREE.VectorKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string
  ): THREE.VectorKeyframeTrack {
    // 复制时间和值
    const times = track.times.slice();
    const values = track.values.slice();

    // 获取骨骼映射
    const mapping = this.config.boneMapping.find(m => m.source === sourceBoneName);
    if (!mapping) return null;

    // 获取骨骼
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();
    const sourceBone = sourceBones.find(bone => bone.name === sourceBoneName);
    const targetBone = targetBones.find(bone => bone.name === targetBoneName);

    if (!sourceBone || !targetBone) return null;

    // 计算缩放因子
    let scale = 1.0;
    if (this.config.adjustBoneLength && mapping.positionScale === undefined) {
      // 计算骨骼长度比例
      const sourceLength = sourceBone.position.length();
      const targetLength = targetBone.position.length();
      if (sourceLength > 0 && targetLength > 0) {
        scale = targetLength / sourceLength;
      }
    } else if (mapping.positionScale !== undefined) {
      scale = mapping.positionScale;
    }

    // 处理每一帧
    for (let i = 0; i < track.times.length; i++) {
      const position = new THREE.Vector3();
      position.fromArray(track.values, i * 3);

      // 应用缩放
      position.multiplyScalar(scale);

      // 如果是镜像，则翻转X轴
      if (mapping.mirror) {
        position.x *= -1;
      }

      // 如果是根骨骼且需要调整高度
      if (sourceBoneName === 'Hips' && this.config.adjustRootHeight) {
        // 调整Y轴高度
        const sourceHeight = sourceBone.position.y;
        const targetHeight = targetBone.position.y;
        position.y = position.y - sourceHeight + targetHeight;
      }

      // 存储结果
      position.toArray(values, i * 3);
    }

    // 创建新轨道
    return new THREE.VectorKeyframeTrack(
      `${targetBoneName}.position`,
      times,
      values
    );
  }

  /**
   * 重定向缩放轨道
   * @param track 缩放轨道
   * @param sourceBoneName 源骨骼名称
   * @param targetBoneName 目标骨骼名称
   * @returns 重定向后的缩放轨道
   */
  private retargetScaleTrack(
    track: THREE.VectorKeyframeTrack,
    sourceBoneName: string,
    targetBoneName: string
  ): THREE.VectorKeyframeTrack {
    // 复制时间和值
    const times = track.times.slice();
    let values = track.values.slice();

    // 如果启用了骨骼长度调整，计算缩放比例
    if (this.config.adjustBoneLength) {
      const sourceBoneLength = this.getBoneLength(sourceBoneName);
      const targetBoneLength = this.getBoneLength(targetBoneName);

      if (sourceBoneLength > 0 && targetBoneLength > 0) {
        const scaleRatio = targetBoneLength / sourceBoneLength;

        // 应用缩放比例
        for (let i = 0; i < values.length; i += 3) {
          values[i] *= scaleRatio;     // x
          values[i + 1] *= scaleRatio; // y
          values[i + 2] *= scaleRatio; // z
        }
      }
    }

    // 创建新轨道
    return new THREE.VectorKeyframeTrack(
      `${targetBoneName}.scale`,
      times,
      values
    );
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public addEventListener(type: RetargetEventType, callback: (data: any) => void): void {
    this.eventEmitter.on(type, callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(type: RetargetEventType, callback: (data: any) => void): void {
    this.eventEmitter.off(type, callback);
  }

  /**
   * 获取配置
   * @returns 重定向配置
   */
  public getConfig(): RetargetConfig {
    return { ...this.config };
  }

  /**
   * 获取骨骼长度
   * @param boneName 骨骼名称
   * @returns 骨骼长度
   */
  private getBoneLength(boneName: string): number {
    // 检查缓存
    if (this.boneLengthCache.has(boneName)) {
      return this.boneLengthCache.get(boneName)!;
    }

    // 获取骨骼
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();

    let bone = sourceBones.find(b => b.name === boneName);
    if (!bone) {
      bone = targetBones.find(b => b.name === boneName);
    }

    if (!bone) {
      return 0;
    }

    // 计算骨骼长度
    let length = 0;
    if (bone.children.length > 0) {
      // 使用第一个子骨骼的距离作为长度
      const child = bone.children[0];
      if (child instanceof THREE.Bone) {
        length = bone.position.distanceTo(child.position);
      }
    } else {
      // 如果没有子骨骼，使用位置向量的长度
      length = bone.position.length();
    }

    // 缓存结果
    this.boneLengthCache.set(boneName, length);
    return length;
  }

  /**
   * 构建骨骼层次结构缓存
   */
  private buildBoneHierarchyCache(): void {
    this.boneHierarchyCache.clear();

    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();
    const allBones = [...sourceBones, ...targetBones];

    // 构建层次结构
    for (const bone of allBones) {
      const children: string[] = [];

      for (const child of bone.children) {
        if (child instanceof THREE.Bone) {
          children.push(child.name);
        }
      }

      this.boneHierarchyCache.set(bone.name, children);
    }
  }

  /**
   * 检测T-Pose
   * @param skeleton 骨骼对象
   * @returns 是否为T-Pose
   */
  public detectTPose(skeleton: THREE.Skeleton | THREE.Bone[]): boolean {
    if (!this.config.enableTPoseDetection) {
      return false;
    }

    const bones = Array.isArray(skeleton) ? skeleton : skeleton.bones;

    // T-Pose检测逻辑
    let tPoseScore = 0;
    let totalBones = 0;

    for (const bone of bones) {
      // 检查关键骨骼的旋转
      if (this.isKeyBone(bone.name)) {
        const rotation = bone.quaternion.clone();
        const expectedTPoseRotation = this.getExpectedTPoseRotation(bone.name);

        if (expectedTPoseRotation) {
          const angleDiff = rotation.angleTo(expectedTPoseRotation);

          // 如果角度差小于阈值，认为是T-Pose
          if (angleDiff < Math.PI / 6) { // 30度阈值
            tPoseScore++;
          }
          totalBones++;
        }
      }
    }

    const isTPose = totalBones > 0 && (tPoseScore / totalBones) > 0.7;

    if (isTPose) {
      // 缓存T-Pose数据
      for (const bone of bones) {
        this.tPoseCache.set(bone.name, bone.quaternion.clone());
      }

      // 触发T-Pose检测事件
      this.eventEmitter.emit(RetargetEventType.TPOSE_DETECTED, {
        skeleton: bones,
        score: tPoseScore / totalBones
      });
    }

    return isTPose;
  }

  /**
   * 检查是否为关键骨骼
   * @param boneName 骨骼名称
   * @returns 是否为关键骨骼
   */
  private isKeyBone(boneName: string): boolean {
    const keyBones = [
      'Hips', 'Spine', 'Chest', 'Neck', 'Head',
      'LeftShoulder', 'LeftArm', 'LeftForeArm', 'LeftHand',
      'RightShoulder', 'RightArm', 'RightForeArm', 'RightHand',
      'LeftUpLeg', 'LeftLeg', 'LeftFoot',
      'RightUpLeg', 'RightLeg', 'RightFoot'
    ];

    return keyBones.some(key => boneName.toLowerCase().includes(key.toLowerCase()));
  }

  /**
   * 获取期望的T-Pose旋转
   * @param boneName 骨骼名称
   * @returns 期望的旋转四元数
   */
  private getExpectedTPoseRotation(boneName: string): THREE.Quaternion | null {
    // 简化的T-Pose旋转定义
    const tPoseRotations: { [key: string]: THREE.Quaternion } = {
      'LeftArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, -Math.PI / 2)),
      'RightArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, Math.PI / 2)),
      'LeftForeArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, 0)),
      'RightForeArm': new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0, 0))
    };

    for (const [key, rotation] of Object.entries(tPoseRotations)) {
      if (boneName.toLowerCase().includes(key.toLowerCase())) {
        return rotation.clone();
      }
    }

    return new THREE.Quaternion(); // 默认无旋转
  }

  /**
   * 高级骨骼映射算法
   * @param sourceBones 源骨骼数组
   * @param targetBones 目标骨骼数组
   * @returns 自动生成的骨骼映射
   */
  public advancedBoneMapping(sourceBones: THREE.Bone[], targetBones: THREE.Bone[]): BoneMapping[] {
    const mappings: BoneMapping[] = [];

    // 1. 精确名称匹配
    for (const sourceBone of sourceBones) {
      const exactMatch = targetBones.find(bone => bone.name === sourceBone.name);
      if (exactMatch) {
        mappings.push({
          source: sourceBone.name,
          target: exactMatch.name
        });
      }
    }

    // 2. 模糊名称匹配
    for (const sourceBone of sourceBones) {
      if (mappings.some(m => m.source === sourceBone.name)) continue;

      const fuzzyMatch = this.findFuzzyBoneMatch(sourceBone.name, targetBones);
      if (fuzzyMatch) {
        mappings.push({
          source: sourceBone.name,
          target: fuzzyMatch.name
        });
      }
    }

    // 3. 层次结构匹配
    for (const sourceBone of sourceBones) {
      if (mappings.some(m => m.source === sourceBone.name)) continue;

      const hierarchyMatch = this.findHierarchyMatch(sourceBone, sourceBones, targetBones, mappings);
      if (hierarchyMatch) {
        mappings.push({
          source: sourceBone.name,
          target: hierarchyMatch.name
        });
      }
    }

    return mappings;
  }

  /**
   * 模糊骨骼名称匹配
   * @param sourceName 源骨骼名称
   * @param targetBones 目标骨骼数组
   * @returns 匹配的目标骨骼
   */
  private findFuzzyBoneMatch(sourceName: string, targetBones: THREE.Bone[]): THREE.Bone | null {
    const sourceNameLower = sourceName.toLowerCase();

    // 定义常见的骨骼名称映射
    const nameMapping: { [key: string]: string[] } = {
      'hips': ['pelvis', 'root', 'hip'],
      'spine': ['spine1', 'spine01', 'back'],
      'chest': ['spine2', 'spine02', 'chest', 'upper_chest'],
      'neck': ['neck1', 'neck01'],
      'head': ['head1', 'head01'],
      'shoulder': ['clavicle', 'collar'],
      'arm': ['upperarm', 'upper_arm'],
      'forearm': ['lowerarm', 'lower_arm'],
      'hand': ['wrist'],
      'thigh': ['upleg', 'upper_leg'],
      'shin': ['leg', 'lower_leg'],
      'foot': ['ankle']
    };

    // 查找最佳匹配
    let bestMatch: THREE.Bone | null = null;
    let bestScore = 0;

    for (const targetBone of targetBones) {
      const targetNameLower = targetBone.name.toLowerCase();
      let score = 0;

      // 直接子字符串匹配
      if (sourceNameLower.includes(targetNameLower) || targetNameLower.includes(sourceNameLower)) {
        score += 0.8;
      }

      // 使用映射表匹配
      for (const [key, aliases] of Object.entries(nameMapping)) {
        if (sourceNameLower.includes(key)) {
          for (const alias of aliases) {
            if (targetNameLower.includes(alias)) {
              score += 0.6;
              break;
            }
          }
        }
      }

      // 左右匹配
      const isSourceLeft = sourceNameLower.includes('left') || sourceNameLower.includes('l_');
      const isSourceRight = sourceNameLower.includes('right') || sourceNameLower.includes('r_');
      const isTargetLeft = targetNameLower.includes('left') || targetNameLower.includes('l_');
      const isTargetRight = targetNameLower.includes('right') || targetNameLower.includes('r_');

      if ((isSourceLeft && isTargetLeft) || (isSourceRight && isTargetRight)) {
        score += 0.3;
      }

      if (score > bestScore) {
        bestScore = score;
        bestMatch = targetBone;
      }
    }

    return bestScore > 0.5 ? bestMatch : null;
  }

  /**
   * 层次结构匹配
   * @param sourceBone 源骨骼
   * @param sourceBones 源骨骼数组
   * @param targetBones 目标骨骼数组
   * @param existingMappings 已存在的映射
   * @returns 匹配的目标骨骼
   */
  private findHierarchyMatch(
    sourceBone: THREE.Bone,
    sourceBones: THREE.Bone[],
    targetBones: THREE.Bone[],
    existingMappings: BoneMapping[]
  ): THREE.Bone | null {
    // 查找父骨骼的映射
    if (sourceBone.parent && sourceBone.parent instanceof THREE.Bone) {
      const parentMapping = existingMappings.find(m => m.source === sourceBone.parent.name);
      if (parentMapping) {
        // 在目标骨骼中查找对应父骨骼的子骨骼
        const targetParent = targetBones.find(bone => bone.name === parentMapping.target);
        if (targetParent) {
          // 查找最相似的子骨骼
          for (const child of targetParent.children) {
            if (child instanceof THREE.Bone) {
              const similarity = this.calculateBoneSimilarity(sourceBone, child);
              if (similarity > 0.7) {
                return child;
              }
            }
          }
        }
      }
    }

    return null;
  }

  /**
   * 计算骨骼相似度
   * @param bone1 骨骼1
   * @param bone2 骨骼2
   * @returns 相似度分数 (0-1)
   */
  private calculateBoneSimilarity(bone1: THREE.Bone, bone2: THREE.Bone): number {
    let score = 0;

    // 位置相似度
    const positionDistance = bone1.position.distanceTo(bone2.position);
    const positionSimilarity = Math.max(0, 1 - positionDistance / 10); // 假设最大距离为10
    score += positionSimilarity * 0.3;

    // 旋转相似度
    const rotationDifference = bone1.quaternion.angleTo(bone2.quaternion);
    const rotationSimilarity = Math.max(0, 1 - rotationDifference / Math.PI);
    score += rotationSimilarity * 0.3;

    // 子骨骼数量相似度
    const childrenCountDiff = Math.abs(bone1.children.length - bone2.children.length);
    const childrenSimilarity = Math.max(0, 1 - childrenCountDiff / 5); // 假设最大差异为5
    score += childrenSimilarity * 0.2;

    // 名称相似度
    const nameSimilarity = this.calculateNameSimilarity(bone1.name, bone2.name);
    score += nameSimilarity * 0.2;

    return score;
  }

  /**
   * 计算名称相似度
   * @param name1 名称1
   * @param name2 名称2
   * @returns 相似度分数 (0-1)
   */
  private calculateNameSimilarity(name1: string, name2: string): number {
    const n1 = name1.toLowerCase();
    const n2 = name2.toLowerCase();

    // 使用编辑距离算法
    const editDistance = this.calculateEditDistance(n1, n2);
    const maxLength = Math.max(n1.length, n2.length);

    return maxLength > 0 ? 1 - (editDistance / maxLength) : 0;
  }

  /**
   * 计算编辑距离
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 编辑距离
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];

    for (let i = 0; i <= str1.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str1.length; i++) {
      for (let j = 1; j <= str2.length; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,     // 删除
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j - 1] + 1  // 替换
          );
        }
      }
    }

    return matrix[str1.length][str2.length];
  }

  /**
   * 批量重定向动画片段
   * @param clips 动画片段数组
   * @param options 批量处理选项
   * @returns 重定向后的动画片段数组
   */
  public batchRetarget(
    clips: THREE.AnimationClip[],
    options: {
      useCache?: boolean;
      parallel?: boolean;
      onProgress?: (progress: number, current: number, total: number) => void;
      onError?: (error: Error, clipName: string) => void;
    } = {}
  ): THREE.AnimationClip[] {
    const results: THREE.AnimationClip[] = [];
    const { useCache = true, parallel = false, onProgress, onError } = options;

    if (parallel) {
      // 并行处理
      const promises = clips.map(async (clip, index) => {
        try {
          const result = await this.retargetAsync(clip, useCache);
          if (onProgress) {
            onProgress((index + 1) / clips.length, index + 1, clips.length);
          }
          return result;
        } catch (error) {
          if (onError) {
            onError(error as Error, clip.name);
          }
          return null;
        }
      });

      return Promise.all(promises).then(results =>
        results.filter(result => result !== null) as THREE.AnimationClip[]
      ) as any;
    } else {
      // 串行处理
      for (let i = 0; i < clips.length; i++) {
        try {
          const result = this.retargetWithCache(clips[i], useCache);
          results.push(result);

          if (onProgress) {
            onProgress((i + 1) / clips.length, i + 1, clips.length);
          }
        } catch (error) {
          if (onError) {
            onError(error as Error, clips[i].name);
          }
        }
      }
    }

    return results;
  }

  /**
   * 异步重定向动画片段
   * @param clip 动画片段
   * @param useCache 是否使用缓存
   * @returns 重定向后的动画片段
   */
  private async retargetAsync(clip: THREE.AnimationClip, useCache: boolean = true): Promise<THREE.AnimationClip> {
    return new Promise((resolve, reject) => {
      try {
        const result = this.retargetWithCache(clip, useCache);
        resolve(result);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 带缓存的重定向
   * @param clip 动画片段
   * @param useCache 是否使用缓存
   * @returns 重定向后的动画片段
   */
  private retargetWithCache(clip: THREE.AnimationClip, useCache: boolean = true): THREE.AnimationClip {
    if (useCache && this.config.enableCache) {
      const cacheKey = this.generateCacheKey(clip);

      if (this.retargetCache.has(cacheKey)) {
        return this.retargetCache.get(cacheKey)!.clone();
      }

      const result = this.retarget(clip);
      this.retargetCache.set(cacheKey, result.clone());
      return result;
    }

    return this.retarget(clip);
  }

  /**
   * 生成缓存键
   * @param clip 动画片段
   * @returns 缓存键
   */
  private generateCacheKey(clip: THREE.AnimationClip): string {
    const configHash = this.hashConfig();
    return `${clip.name}_${clip.duration}_${configHash}`;
  }

  /**
   * 计算配置哈希
   * @returns 配置哈希值
   */
  private hashConfig(): string {
    const configStr = JSON.stringify({
      boneMapping: this.config.boneMapping,
      preservePositionTracks: this.config.preservePositionTracks,
      preserveScaleTracks: this.config.preserveScaleTracks,
      normalizeRotations: this.config.normalizeRotations,
      adjustRootHeight: this.config.adjustRootHeight,
      adjustBoneLength: this.config.adjustBoneLength
    });

    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < configStr.length; i++) {
      const char = configStr.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return hash.toString(36);
  }

  /**
   * 评估重定向质量
   * @param sourceClip 源动画片段
   * @param retargetedClip 重定向后的动画片段
   * @returns 质量评估结果
   */
  public assessRetargetingQuality(
    sourceClip: THREE.AnimationClip,
    retargetedClip: THREE.AnimationClip
  ): RetargetQualityAssessment {
    const assessment: RetargetQualityAssessment = {
      overallScore: 0,
      boneMappingQuality: 0,
      proportionConsistency: 0,
      animationFidelity: 0,
      detailedReport: {
        unmappedBones: [],
        scaleMismatches: [],
        rotationErrors: [],
        warnings: []
      }
    };

    // 1. 评估骨骼映射质量
    assessment.boneMappingQuality = this.assessBoneMappingQuality(sourceClip, retargetedClip);

    // 2. 评估比例一致性
    assessment.proportionConsistency = this.assessProportionConsistency();

    // 3. 评估动画保真度
    assessment.animationFidelity = this.assessAnimationFidelity(sourceClip, retargetedClip);

    // 4. 计算总体分数
    assessment.overallScore = (
      assessment.boneMappingQuality * 0.4 +
      assessment.proportionConsistency * 0.3 +
      assessment.animationFidelity * 0.3
    );

    // 5. 生成详细报告
    this.generateDetailedReport(sourceClip, retargetedClip, assessment);

    // 缓存评估结果
    this.qualityAssessment = assessment;

    // 触发质量评估事件
    this.eventEmitter.emit(RetargetEventType.QUALITY_ASSESSED, assessment);

    return assessment;
  }

  /**
   * 评估骨骼映射质量
   * @param sourceClip 源动画片段
   * @param retargetedClip 重定向后的动画片段
   * @returns 映射质量分数 (0-1)
   */
  private assessBoneMappingQuality(sourceClip: THREE.AnimationClip, retargetedClip: THREE.AnimationClip): number {
    const sourceTrackCount = sourceClip.tracks.length;
    const retargetedTrackCount = retargetedClip.tracks.length;

    if (sourceTrackCount === 0) return 1;

    // 计算映射覆盖率
    const mappingCoverage = retargetedTrackCount / sourceTrackCount;

    // 检查关键骨骼是否被映射
    const keyBonesMapped = this.checkKeyBonesMapped(sourceClip, retargetedClip);

    return Math.min(1, mappingCoverage * 0.7 + keyBonesMapped * 0.3);
  }

  /**
   * 检查关键骨骼是否被映射
   * @param sourceClip 源动画片段
   * @param retargetedClip 重定向后的动画片段
   * @returns 关键骨骼映射分数 (0-1)
   */
  private checkKeyBonesMapped(sourceClip: THREE.AnimationClip, retargetedClip: THREE.AnimationClip): number {
    const keyBones = ['Hips', 'Spine', 'Head', 'LeftArm', 'RightArm', 'LeftLeg', 'RightLeg'];
    let mappedKeyBones = 0;

    for (const keyBone of keyBones) {
      const hasSourceTrack = sourceClip.tracks.some(track =>
        track.name.toLowerCase().includes(keyBone.toLowerCase())
      );

      if (hasSourceTrack) {
        const hasRetargetedTrack = retargetedClip.tracks.some(track =>
          track.name.toLowerCase().includes(keyBone.toLowerCase())
        );

        if (hasRetargetedTrack) {
          mappedKeyBones++;
        }
      }
    }

    return keyBones.length > 0 ? mappedKeyBones / keyBones.length : 1;
  }

  /**
   * 评估比例一致性
   * @returns 比例一致性分数 (0-1)
   */
  private assessProportionConsistency(): number {
    const sourceBones = this.getSourceBones();
    const targetBones = this.getTargetBones();

    let totalConsistency = 0;
    let comparedBones = 0;

    for (const mapping of this.config.boneMapping) {
      const sourceBone = sourceBones.find(bone => bone.name === mapping.source);
      const targetBone = targetBones.find(bone => bone.name === mapping.target);

      if (sourceBone && targetBone) {
        const sourceLength = this.getBoneLength(sourceBone.name);
        const targetLength = this.getBoneLength(targetBone.name);

        if (sourceLength > 0 && targetLength > 0) {
          const ratio = Math.min(sourceLength, targetLength) / Math.max(sourceLength, targetLength);
          totalConsistency += ratio;
          comparedBones++;
        }
      }
    }

    return comparedBones > 0 ? totalConsistency / comparedBones : 1;
  }

  /**
   * 评估动画保真度
   * @param sourceClip 源动画片段
   * @param retargetedClip 重定向后的动画片段
   * @returns 动画保真度分数 (0-1)
   */
  private assessAnimationFidelity(sourceClip: THREE.AnimationClip, retargetedClip: THREE.AnimationClip): number {
    // 简化的保真度评估
    // 比较关键帧数量和时间分布

    let totalFidelity = 0;
    let comparedTracks = 0;

    for (const sourceTrack of sourceClip.tracks) {
      const boneName = sourceTrack.name.split('.')[0];
      const targetBoneName = this.boneMappingCache.get(boneName);

      if (targetBoneName) {
        const retargetedTrack = retargetedClip.tracks.find(track =>
          track.name.startsWith(targetBoneName)
        );

        if (retargetedTrack) {
          // 比较关键帧数量
          const keyframeRatio = Math.min(sourceTrack.times.length, retargetedTrack.times.length) /
                               Math.max(sourceTrack.times.length, retargetedTrack.times.length);

          // 比较时间分布
          const timeDistributionSimilarity = this.compareTimeDistribution(sourceTrack.times, retargetedTrack.times);

          totalFidelity += (keyframeRatio * 0.5 + timeDistributionSimilarity * 0.5);
          comparedTracks++;
        }
      }
    }

    return comparedTracks > 0 ? totalFidelity / comparedTracks : 0;
  }

  /**
   * 比较时间分布相似性
   * @param times1 时间数组1
   * @param times2 时间数组2
   * @returns 相似性分数 (0-1)
   */
  private compareTimeDistribution(times1: Float32Array | number[], times2: Float32Array | number[]): number {
    if (times1.length === 0 || times2.length === 0) return 0;

    // 简化比较：比较开始时间、结束时间和中点时间
    const start1 = times1[0];
    const end1 = times1[times1.length - 1];
    const mid1 = times1[Math.floor(times1.length / 2)];

    const start2 = times2[0];
    const end2 = times2[times2.length - 1];
    const mid2 = times2[Math.floor(times2.length / 2)];

    const startSimilarity = 1 - Math.abs(start1 - start2) / Math.max(end1, end2);
    const endSimilarity = 1 - Math.abs(end1 - end2) / Math.max(end1, end2);
    const midSimilarity = 1 - Math.abs(mid1 - mid2) / Math.max(end1, end2);

    return (startSimilarity + endSimilarity + midSimilarity) / 3;
  }

  /**
   * 生成详细报告
   * @param sourceClip 源动画片段
   * @param retargetedClip 重定向后的动画片段
   * @param assessment 评估结果
   */
  private generateDetailedReport(
    sourceClip: THREE.AnimationClip,
    retargetedClip: THREE.AnimationClip,
    assessment: RetargetQualityAssessment
  ): void {
    // 查找未映射的骨骼
    for (const track of sourceClip.tracks) {
      const boneName = track.name.split('.')[0];
      const targetBoneName = this.boneMappingCache.get(boneName);

      if (!targetBoneName) {
        assessment.detailedReport.unmappedBones.push(boneName);
      }
    }

    // 查找比例不匹配
    for (const mapping of this.config.boneMapping) {
      const sourceLength = this.getBoneLength(mapping.source);
      const targetLength = this.getBoneLength(mapping.target);

      if (sourceLength > 0 && targetLength > 0) {
        const ratio = targetLength / sourceLength;
        if (ratio < 0.5 || ratio > 2.0) {
          assessment.detailedReport.scaleMismatches.push({
            bone: mapping.source,
            ratio
          });
        }
      }
    }

    // 添加警告
    if (assessment.overallScore < 0.7) {
      assessment.detailedReport.warnings.push('重定向质量较低，建议检查骨骼映射');
    }

    if (assessment.detailedReport.unmappedBones.length > 0) {
      assessment.detailedReport.warnings.push(`${assessment.detailedReport.unmappedBones.length} 个骨骼未被映射`);
    }

    if (assessment.detailedReport.scaleMismatches.length > 0) {
      assessment.detailedReport.warnings.push(`${assessment.detailedReport.scaleMismatches.length} 个骨骼存在比例不匹配`);
    }
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.retargetCache.clear();
    this.boneLengthCache.clear();
    this.tPoseCache.clear();
    this.boneHierarchyCache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  public getCacheStats(): {
    retargetCacheSize: number;
    boneLengthCacheSize: number;
    tPoseCacheSize: number;
    hierarchyCacheSize: number;
  } {
    return {
      retargetCacheSize: this.retargetCache.size,
      boneLengthCacheSize: this.boneLengthCache.size,
      tPoseCacheSize: this.tPoseCache.size,
      hierarchyCacheSize: this.boneHierarchyCache.size
    };
  }

  /**
   * 获取质量评估结果
   * @returns 质量评估结果
   */
  public getQualityAssessment(): RetargetQualityAssessment | null {
    return this.qualityAssessment;
  }
}
