/**
 * 动画状态机
 * 用于管理动画状态和状态之间的转换
 */
import { EventEmitter } from '../utils/EventEmitter';

import { Animator } from './Animator';

/**
 * 动画状态接口
 */
export interface AnimationState {
  /** 状态名称 */
  name: string;
  /** 状态类型 */
  type: string;
  /** 状态权重 */
  weight?: number;
  /** 状态标签 */
  tags?: string[];
  /** 状态进入事件 */
  onEnter?: () => void;
  /** 状态退出事件 */
  onExit?: () => void;
  /** 状态更新事件 */
  onUpdate?: (deltaTime: number) => void;
  /** 状态数据 */
  [key: string]: any;
}

/**
 * 单一动画状态
 */
export interface SingleAnimationState extends AnimationState {
  /** 状态类型 */
  type: 'SingleAnimationState';
  /** 动画片段名称 */
  clipName: string;
  /** 是否循环 */
  loop: boolean;
  /** 播放完成后是否保持最后一帧 */
  clamp: boolean;
}

/**
 * 混合动画状态
 */
export interface BlendAnimationState extends AnimationState {
  /** 状态类型 */
  type: 'BlendAnimationState';
  /** 混合参数名称 */
  parameterName: string;
  /** 混合空间类型 */
  blendSpaceType: '1D' | '2D';
  /** 混合空间配置 */
  blendSpaceConfig: BlendSpaceConfig;
}

/**
 * 复合状态
 */
export interface CompositeState extends AnimationState {
  /** 状态类型 */
  type: 'CompositeState';
  /** 子状态 */
  subStates: AnimationState[];
  /** 默认子状态 */
  defaultSubState?: string;
}

/**
 * 并行状态
 */
export interface ParallelState extends AnimationState {
  /** 状态类型 */
  type: 'ParallelState';
  /** 并行动画列表 */
  parallelAnimations: {
    clipName: string;
    weight: number;
    layer?: number;
  }[];
}

/**
 * 混合空间配置
 */
export interface BlendSpaceConfig {
  /** 混合点 */
  blendPoints: BlendPoint[];
  /** 插值类型 */
  interpolationType: 'linear' | 'cubic' | 'smoothstep';
  /** 是否启用三角剖分（2D混合空间） */
  enableTriangulation?: boolean;
}

/**
 * 混合点
 */
export interface BlendPoint {
  /** 动画片段名称 */
  clipName: string;
  /** 位置（1D为单个值，2D为[x, y]） */
  position: number | [number, number];
  /** 权重 */
  weight?: number;
}

/**
 * 状态转换规则
 */
export interface TransitionRule {
  /** 源状态名称 */
  from: string;
  /** 目标状态名称 */
  to: string;
  /** 转换条件 */
  condition: () => boolean;
  /** 条件表达式（用于序列化） */
  conditionExpression?: string;
  /** 转换持续时间（秒） */
  duration: number;
  /** 是否可以中断 */
  canInterrupt: boolean;
  /** 转换曲线类型 */
  curveType?: TransitionCurveType;
  /** 优先级 */
  priority?: number;
  /** 转换偏移时间 */
  offset?: number;
  /** 是否有退出时间 */
  hasExitTime?: boolean;
  /** 退出时间（0-1，相对于动画长度） */
  exitTime?: number;
  /** 转换模式 */
  mode?: TransitionMode;
}

/**
 * 转换曲线类型
 */
export enum TransitionCurveType {
  LINEAR = 'linear',
  EASE_IN = 'easeIn',
  EASE_OUT = 'easeOut',
  EASE_IN_OUT = 'easeInOut',
  SMOOTH_STEP = 'smoothStep',
  CUSTOM = 'custom'
}

/**
 * 转换模式
 */
export enum TransitionMode {
  /** 立即转换 */
  IMMEDIATE = 'immediate',
  /** 等待当前动画完成 */
  WAIT_FOR_COMPLETION = 'waitForCompletion',
  /** 在指定时间点转换 */
  AT_TIME = 'atTime',
  /** 在动画事件触发时转换 */
  ON_EVENT = 'onEvent'
}

/**
 * 动画状态机事件类型
 */
export enum AnimationStateMachineEventType {
  /** 状态进入 */
  STATE_ENTER = 'stateEnter',
  /** 状态退出 */
  STATE_EXIT = 'stateExit',
  /** 状态转换开始 */
  TRANSITION_START = 'transitionStart',
  /** 状态转换结束 */
  TRANSITION_END = 'transitionEnd',
  /** 动画事件触发 */
  ANIMATION_EVENT = 'animationEvent',
  /** 参数变化 */
  PARAMETER_CHANGED = 'parameterChanged',
  /** 状态机重置 */
  STATE_MACHINE_RESET = 'stateMachineReset'
}

/**
 * 动画事件
 */
export interface AnimationEvent {
  /** 事件名称 */
  name: string;
  /** 触发时间（相对于动画长度，0-1） */
  time: number;
  /** 事件数据 */
  data?: any;
  /** 事件回调 */
  callback?: (data?: any) => void;
}

/**
 * 状态机配置
 */
export interface StateMachineConfig {
  /** 默认状态 */
  defaultState?: string;
  /** 是否启用调试模式 */
  debugMode?: boolean;
  /** 是否启用状态缓存 */
  enableStateCache?: boolean;
  /** 最大转换队列长度 */
  maxTransitionQueue?: number;
  /** 是否启用并行处理 */
  enableParallelProcessing?: boolean;
}

/**
 * 参数元数据
 */
export interface ParameterMetadata {
  /** 最小值（仅适用于数值类型） */
  minValue?: number;
  /** 最大值（仅适用于数值类型） */
  maxValue?: number;
  /** 枚举值（仅适用于枚举类型） */
  enumValues?: string[];
  /** 描述 */
  description?: string;
}

/**
 * 动画状态机
 */
export class AnimationStateMachine {
  /** 状态映射 */
  private states: Map<string, AnimationState> = new Map();
  /** 转换规则列表 */
  private transitions: TransitionRule[] = [];
  /** 当前状态 */
  private currentState: AnimationState | null = null;
  /** 上一个状态 */
  private previousState: AnimationState | null = null;
  /** 是否正在转换 */
  private isTransitioning: boolean = false;
  /** 当前转换规则 */
  private currentTransition: TransitionRule | null = null;
  /** 转换开始时间 */
  private transitionStartTime: number = 0;
  /** 转换持续时间 */
  private transitionDuration: number = 0;
  /** 动画控制器 */
  private animator: Animator;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 参数映射 */
  private parameters: Map<string, any> = new Map();
  /** 参数元数据映射 */
  private parameterMetadata: Map<string, ParameterMetadata> = new Map();
  /** 调试模式 */
  private debugMode: boolean = false;
  /** 状态机配置 */
  private config: StateMachineConfig;
  /** 状态缓存 */
  private stateCache: Map<string, any> = new Map();
  /** 转换队列 */
  private transitionQueue: TransitionRule[] = [];
  /** 动画事件映射 */
  private animationEvents: Map<string, AnimationEvent[]> = new Map();
  /** 条件表达式解析器 */
  private conditionParser: ConditionParser | null = null;
  /** 混合空间处理器 */
  private blendSpaceProcessor: BlendSpaceProcessor | null = null;
  /** 转换曲线处理器 */
  private transitionCurveProcessor: TransitionCurveProcessor | null = null;

  /**
   * 构造函数
   * @param animator 动画控制器
   * @param config 状态机配置
   */
  constructor(animator: Animator, config: StateMachineConfig = {}) {
    this.animator = animator;
    this.config = {
      defaultState: config.defaultState,
      debugMode: config.debugMode || false,
      enableStateCache: config.enableStateCache || true,
      maxTransitionQueue: config.maxTransitionQueue || 10,
      enableParallelProcessing: config.enableParallelProcessing || false
    };

    this.debugMode = this.config.debugMode || false;

    // 初始化处理器
    this.initializeProcessors();
  }

  /**
   * 初始化处理器
   */
  private initializeProcessors(): void {
    this.conditionParser = new ConditionParser();
    this.blendSpaceProcessor = new BlendSpaceProcessor();
    this.transitionCurveProcessor = new TransitionCurveProcessor();
  }

  /**
   * 添加状态
   * @param state 动画状态
   */
  public addState(state: AnimationState): void {
    this.states.set(state.name, state);
  }

  /**
   * 添加转换规则
   * @param rule 转换规则
   */
  public addTransition(rule: TransitionRule): void {
    this.transitions.push(rule);
  }

  /**
   * 设置参数
   * @param name 参数名称
   * @param value 参数值
   */
  public setParameter(name: string, value: any): void {
    this.parameters.set(name, value);
  }

  /**
   * 获取参数
   * @param name 参数名称
   * @returns 参数值
   */
  public getParameter(name: string): any {
    return this.parameters.get(name);
  }

  /**
   * 设置当前状态
   * @param stateName 状态名称
   */
  public setCurrentState(stateName: string): void {
    const state = this.states.get(stateName);
    if (!state) {
      console.warn(`状态 "${stateName}" 不存在`);
      return;
    }

    if (this.currentState) {
      this.previousState = this.currentState;
      this.eventEmitter.emit(AnimationStateMachineEventType.STATE_EXIT, this.currentState);
    }

    this.currentState = state;
    this.isTransitioning = false;
    this.currentTransition = null;

    this.enterState(state);
    this.eventEmitter.emit(AnimationStateMachineEventType.STATE_ENTER, state);
  }

  /**
   * 更新状态机
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    if (!this.currentState) return;

    // 如果正在转换中
    if (this.isTransitioning && this.currentTransition) {
      const transitionTime = this.animator.getTime() - this.transitionStartTime;
      const progress = Math.min(transitionTime / this.transitionDuration, 1.0);

      // 如果转换完成
      if (progress >= 1.0) {
        this.isTransitioning = false;
        this.setCurrentState(this.currentTransition.to);
        this.eventEmitter.emit(AnimationStateMachineEventType.TRANSITION_END, {
          from: this.currentTransition.from,
          to: this.currentTransition.to
        });
        this.currentTransition = null;
      }
    } else {
      // 检查是否有满足条件的转换规则
      for (const transition of this.transitions) {
        if (transition.from === this.currentState.name && transition.condition()) {
          this.startTransition(transition);
          break;
        }
      }
    }

    // 更新当前状态
    this.updateState(this.currentState, deltaTime);
  }

  /**
   * 开始转换
   * @param transition 转换规则
   */
  private startTransition(transition: TransitionRule): void {
    this.isTransitioning = true;
    this.currentTransition = transition;
    this.transitionStartTime = this.animator.getTime();
    this.transitionDuration = transition.duration;

    const targetState = this.states.get(transition.to);
    if (!targetState) {
      console.warn(`目标状态 "${transition.to}" 不存在`);
      return;
    }

    // 开始混合到目标状态
    this.blendToState(targetState, transition.duration);

    this.eventEmitter.emit(AnimationStateMachineEventType.TRANSITION_START, {
      from: transition.from,
      to: transition.to,
      duration: transition.duration
    });
  }

  /**
   * 进入状态
   * @param state 动画状态
   */
  private enterState(state: AnimationState): void {
    // 调用状态进入事件
    if (state.onEnter) {
      state.onEnter();
    }

    // 根据状态类型处理
    switch (state.type) {
      case 'SingleAnimationState':
        this.enterSingleAnimationState(state as SingleAnimationState);
        break;
      case 'BlendAnimationState':
        this.enterBlendAnimationState(state as BlendAnimationState);
        break;
      case 'CompositeState':
        this.enterCompositeState(state as CompositeState);
        break;
      case 'ParallelState':
        this.enterParallelState(state as ParallelState);
        break;
      default:
        console.warn(`未知的状态类型: ${state.type}`);
    }

    // 触发动画事件
    this.triggerAnimationEvents(state.name);
  }

  /**
   * 进入单一动画状态
   * @param state 单一动画状态
   */
  private enterSingleAnimationState(state: SingleAnimationState): void {
    this.animator.play(state.clipName, 0);
    this.animator.setLoop(state.loop);
  }

  /**
   * 进入混合动画状态
   * @param state 混合动画状态
   */
  private enterBlendAnimationState(state: BlendAnimationState): void {
    this.updateBlendState(state);
  }

  /**
   * 进入复合状态
   * @param state 复合状态
   */
  private enterCompositeState(state: CompositeState): void {
    // 进入默认子状态
    if (state.defaultSubState) {
      const subState = state.subStates.find(s => s.name === state.defaultSubState);
      if (subState) {
        this.enterState(subState);
      }
    } else if (state.subStates.length > 0) {
      this.enterState(state.subStates[0]);
    }
  }

  /**
   * 进入并行状态
   * @param state 并行状态
   */
  private enterParallelState(state: ParallelState): void {
    // 播放所有并行动画
    for (const animation of state.parallelAnimations) {
      this.animator.play(animation.clipName, 0, animation.weight, animation.layer);
    }
  }

  /**
   * 更新状态
   * @param state 动画状态
   * @param deltaTime 时间增量（秒）
   */
  private updateState(state: AnimationState, _deltaTime: number): void {
    if (state.type === 'BlendAnimationState') {
      const blendState = state as BlendAnimationState;
      this.updateBlendState(blendState);
    }
  }

  /**
   * 更新混合状态
   * @param state 混合动画状态
   */
  private updateBlendState(state: BlendAnimationState): void {
    const paramValue = this.getParameter(state.parameterName);
    if (paramValue === undefined) return;

    if (state.blendSpaceType === '1D') {
      this.update1DBlendSpace(state, paramValue);
    } else if (state.blendSpaceType === '2D') {
      this.update2DBlendSpace(state, paramValue);
    }
  }

  /**
   * 更新1D混合空间
   * @param state 混合动画状态
   * @param paramValue 参数值
   */
  private update1DBlendSpace(state: BlendAnimationState, paramValue: number): void {
    const config = state.blendSpaceConfig;
    const blendPoints = config.blendPoints;

    // 排序混合点
    const sortedPoints = blendPoints
      .filter(point => typeof point.position === 'number')
      .sort((a, b) => (a.position as number) - (b.position as number));

    if (sortedPoints.length === 0) return;

    // 找到相邻的混合点
    let leftPoint: BlendPoint | null = null;
    let rightPoint: BlendPoint | null = null;

    for (let i = 0; i < sortedPoints.length; i++) {
      const point = sortedPoints[i];
      const position = point.position as number;

      if (position <= paramValue) {
        leftPoint = point;
      }
      if (position >= paramValue && !rightPoint) {
        rightPoint = point;
        break;
      }
    }

    // 计算混合权重
    if (leftPoint && rightPoint && leftPoint !== rightPoint) {
      const leftPos = leftPoint.position as number;
      const rightPos = rightPoint.position as number;
      const t = (paramValue - leftPos) / (rightPos - leftPos);

      // 应用插值
      const leftWeight = this.applyInterpolation(1 - t, config.interpolationType);
      const rightWeight = this.applyInterpolation(t, config.interpolationType);

      // 播放混合动画
      this.playBlendedAnimation([
        { clipName: leftPoint.clipName, weight: leftWeight },
        { clipName: rightPoint.clipName, weight: rightWeight }
      ]);
    } else if (leftPoint) {
      // 只有左侧点，直接播放
      this.animator.play(leftPoint.clipName, 0);
    } else if (rightPoint) {
      // 只有右侧点，直接播放
      this.animator.play(rightPoint.clipName, 0);
    }
  }

  /**
   * 更新2D混合空间
   * @param state 混合动画状态
   * @param paramValue 参数值（[x, y]）
   */
  private update2DBlendSpace(state: BlendAnimationState, paramValue: [number, number]): void {
    const config = state.blendSpaceConfig;
    const blendPoints = config.blendPoints;

    // 过滤2D混合点
    const points2D = blendPoints.filter(point => Array.isArray(point.position));

    if (points2D.length === 0) return;

    if (config.enableTriangulation) {
      // 使用三角剖分
      this.updateBlendSpaceWithTriangulation(points2D, paramValue);
    } else {
      // 使用距离权重
      this.updateBlendSpaceWithDistanceWeight(points2D, paramValue);
    }
  }

  /**
   * 使用三角剖分更新混合空间
   * @param points 混合点
   * @param paramValue 参数值
   */
  private updateBlendSpaceWithTriangulation(points: BlendPoint[], paramValue: [number, number]): void {
    // 简化的三角剖分实现
    // 找到包含参数点的三角形
    const triangle = this.findContainingTriangle(points, paramValue);

    if (triangle) {
      const weights = this.calculateBarycentricWeights(triangle, paramValue);
      const blendData = triangle.map((point, index) => ({
        clipName: point.clipName,
        weight: weights[index]
      }));

      this.playBlendedAnimation(blendData);
    }
  }

  /**
   * 使用距离权重更新混合空间
   * @param points 混合点
   * @param paramValue 参数值
   */
  private updateBlendSpaceWithDistanceWeight(points: BlendPoint[], paramValue: [number, number]): void {
    const weights: { clipName: string; weight: number }[] = [];
    let totalWeight = 0;

    // 计算每个点的权重
    for (const point of points) {
      const pos = point.position as [number, number];
      const distance = Math.sqrt(
        Math.pow(paramValue[0] - pos[0], 2) + Math.pow(paramValue[1] - pos[1], 2)
      );

      // 使用反距离权重
      const weight = distance === 0 ? 1 : 1 / (distance + 0.001);
      weights.push({ clipName: point.clipName, weight });
      totalWeight += weight;
    }

    // 归一化权重
    if (totalWeight > 0) {
      weights.forEach(w => w.weight /= totalWeight);
      this.playBlendedAnimation(weights);
    }
  }

  /**
   * 混合到状态
   * @param state 目标状态
   * @param duration 混合持续时间（秒）
   */
  private blendToState(state: AnimationState, duration: number): void {
    if (state.type === 'SingleAnimationState') {
      const singleState = state as SingleAnimationState;
      this.animator.play(singleState.clipName, duration);
    } else if (state.type === 'BlendAnimationState') {
      // 处理混合到混合状态的逻辑
    }
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public addEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void {
    this.eventEmitter.on(type, callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void {
    this.eventEmitter.off(type, callback);
  }

  /**
   * 获取所有状态
   * @returns 状态数组
   */
  public getStates(): AnimationState[] {
    return Array.from(this.states.values());
  }

  /**
   * 获取状态
   * @param name 状态名称
   * @returns 状态，如果不存在则返回null
   */
  public getState(name: string): AnimationState | null {
    return this.states.get(name) || null;
  }

  /**
   * 移除状态
   * @param name 状态名称
   * @returns 是否成功移除
   */
  public removeState(name: string): boolean {
    // 检查是否存在
    if (!this.states.has(name)) {
      return false;
    }

    // 如果是当前状态，则清除当前状态
    if (this.currentState && this.currentState.name === name) {
      this.currentState = null;
    }

    // 移除相关的转换规则
    this.transitions = this.transitions.filter(
      (transition) => transition.from !== name && transition.to !== name
    );

    // 移除状态
    return this.states.delete(name);
  }

  /**
   * 获取所有转换规则
   * @returns 转换规则数组
   */
  public getTransitions(): TransitionRule[] {
    return [...this.transitions];
  }

  /**
   * 获取转换规则
   * @param fromState 源状态名称
   * @param toState 目标状态名称
   * @returns 转换规则，如果不存在则返回null
   */
  public getTransition(fromState: string, toState: string): TransitionRule | null {
    return this.transitions.find(
      (transition) => transition.from === fromState && transition.to === toState
    ) || null;
  }

  /**
   * 移除转换规则
   * @param fromState 源状态名称
   * @param toState 目标状态名称
   * @returns 是否成功移除
   */
  public removeTransition(fromState: string, toState: string): boolean {
    const index = this.transitions.findIndex(
      (transition) => transition.from === fromState && transition.to === toState
    );

    if (index === -1) {
      return false;
    }

    // 如果是当前转换，则清除当前转换
    if (
      this.currentTransition &&
      this.currentTransition.from === fromState &&
      this.currentTransition.to === toState
    ) {
      this.isTransitioning = false;
      this.currentTransition = null;
    }

    // 移除转换规则
    this.transitions.splice(index, 1);
    return true;
  }

  /**
   * 获取当前状态
   * @returns 当前状态，如果没有则返回null
   */
  public getCurrentState(): AnimationState | null {
    return this.currentState;
  }

  /**
   * 获取上一个状态
   * @returns 上一个状态，如果没有则返回null
   */
  public getPreviousState(): AnimationState | null {
    return this.previousState;
  }

  /**
   * 获取所有参数
   * @returns 参数映射
   */
  public getParameters(): Map<string, any> {
    return new Map(this.parameters);
  }

  /**
   * 移除参数
   * @param name 参数名称
   * @returns 是否成功移除
   */
  public removeParameter(name: string): boolean {
    // 移除参数元数据
    this.parameterMetadata.delete(name);

    // 移除参数
    return this.parameters.delete(name);
  }

  /**
   * 设置参数元数据
   * @param name 参数名称
   * @param metadata 参数元数据
   */
  public setParameterMetadata(name: string, metadata: ParameterMetadata): void {
    this.parameterMetadata.set(name, metadata);
  }

  /**
   * 获取参数元数据
   * @param name 参数名称
   * @returns 参数元数据，如果不存在则返回null
   */
  public getParameterMetadata(name: string): ParameterMetadata | null {
    return this.parameterMetadata.get(name) || null;
  }

  /**
   * 设置调试模式
   * @param enabled 是否启用
   */
  public setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  /**
   * 获取调试模式
   * @returns 是否启用调试模式
   */
  public isDebugMode(): boolean {
    return this.debugMode;
  }

  /**
   * 获取动画控制器
   * @returns 动画控制器
   */
  public getAnimator(): Animator {
    return this.animator;
  }

  /**
   * 重置状态机
   */
  public reset(): void {
    this.currentState = null;
    this.previousState = null;
    this.isTransitioning = false;
    this.currentTransition = null;
  }

  /**
   * 清空状态机
   */
  public clear(): void {
    this.states.clear();
    this.transitions = [];
    this.parameters.clear();
    this.parameterMetadata.clear();
    this.animationEvents.clear();
    this.stateCache.clear();
    this.transitionQueue = [];
    this.reset();
  }

  /**
   * 应用插值
   * @param t 插值参数 (0-1)
   * @param type 插值类型
   * @returns 插值结果
   */
  private applyInterpolation(t: number, type: string): number {
    switch (type) {
      case 'linear':
        return t;
      case 'cubic':
        return t * t * (3 - 2 * t);
      case 'smoothstep':
        return t * t * t * (t * (t * 6 - 15) + 10);
      default:
        return t;
    }
  }

  /**
   * 播放混合动画
   * @param blendData 混合数据
   */
  private playBlendedAnimation(blendData: { clipName: string; weight: number }[]): void {
    // 简化实现：播放权重最高的动画
    let maxWeight = 0;
    let selectedClip = '';

    for (const data of blendData) {
      if (data.weight > maxWeight) {
        maxWeight = data.weight;
        selectedClip = data.clipName;
      }
    }

    if (selectedClip) {
      this.animator.play(selectedClip, 0);
    }
  }

  /**
   * 找到包含参数点的三角形
   * @param points 混合点
   * @param paramValue 参数值
   * @returns 三角形顶点
   */
  private findContainingTriangle(points: BlendPoint[], paramValue: [number, number]): BlendPoint[] | null {
    // 简化实现：返回距离最近的三个点
    const distances = points.map(point => {
      const pos = point.position as [number, number];
      return {
        point,
        distance: Math.sqrt(
          Math.pow(paramValue[0] - pos[0], 2) + Math.pow(paramValue[1] - pos[1], 2)
        )
      };
    });

    distances.sort((a, b) => a.distance - b.distance);
    return distances.slice(0, 3).map(d => d.point);
  }

  /**
   * 计算重心坐标权重
   * @param triangle 三角形顶点
   * @param paramValue 参数值
   * @returns 重心坐标权重
   */
  private calculateBarycentricWeights(triangle: BlendPoint[], paramValue: [number, number]): number[] {
    if (triangle.length !== 3) return [1, 0, 0];

    const p1 = triangle[0].position as [number, number];
    const p2 = triangle[1].position as [number, number];
    const p3 = triangle[2].position as [number, number];

    const denom = (p2[1] - p3[1]) * (p1[0] - p3[0]) + (p3[0] - p2[0]) * (p1[1] - p3[1]);

    if (Math.abs(denom) < 0.001) {
      return [1/3, 1/3, 1/3]; // 退化三角形，平均权重
    }

    const w1 = ((p2[1] - p3[1]) * (paramValue[0] - p3[0]) + (p3[0] - p2[0]) * (paramValue[1] - p3[1])) / denom;
    const w2 = ((p3[1] - p1[1]) * (paramValue[0] - p3[0]) + (p1[0] - p3[0]) * (paramValue[1] - p3[1])) / denom;
    const w3 = 1 - w1 - w2;

    return [w1, w2, w3];
  }

  /**
   * 触发动画事件
   * @param stateName 状态名称
   */
  private triggerAnimationEvents(stateName: string): void {
    const events = this.animationEvents.get(stateName);
    if (events) {
      for (const event of events) {
        if (event.callback) {
          event.callback(event.data);
        }
        this.eventEmitter.emit(AnimationStateMachineEventType.ANIMATION_EVENT, event);
      }
    }
  }

  /**
   * 添加动画事件
   * @param stateName 状态名称
   * @param event 动画事件
   */
  public addAnimationEvent(stateName: string, event: AnimationEvent): void {
    if (!this.animationEvents.has(stateName)) {
      this.animationEvents.set(stateName, []);
    }
    this.animationEvents.get(stateName)!.push(event);
  }

  /**
   * 移除动画事件
   * @param stateName 状态名称
   * @param eventName 事件名称
   * @returns 是否成功移除
   */
  public removeAnimationEvent(stateName: string, eventName: string): boolean {
    const events = this.animationEvents.get(stateName);
    if (events) {
      const index = events.findIndex(e => e.name === eventName);
      if (index !== -1) {
        events.splice(index, 1);
        return true;
      }
    }
    return false;
  }

  /**
   * 序列化状态机
   * @returns 序列化数据
   */
  public serialize(): any {
    return {
      states: Array.from(this.states.entries()),
      transitions: this.transitions.map(t => ({
        ...t,
        condition: undefined, // 函数无法序列化
        conditionExpression: t.conditionExpression || 'true'
      })),
      parameters: Array.from(this.parameters.entries()),
      parameterMetadata: Array.from(this.parameterMetadata.entries()),
      animationEvents: Array.from(this.animationEvents.entries()),
      config: this.config,
      currentState: this.currentState?.name || null
    };
  }

  /**
   * 反序列化状态机
   * @param data 序列化数据
   */
  public deserialize(data: any): void {
    this.clear();

    // 恢复状态
    for (const [name, state] of data.states) {
      this.states.set(name, state);
    }

    // 恢复转换规则
    this.transitions = data.transitions.map((t: any) => ({
      ...t,
      condition: this.parseConditionExpression(t.conditionExpression || 'true')
    }));

    // 恢复参数
    for (const [name, value] of data.parameters) {
      this.parameters.set(name, value);
    }

    // 恢复参数元数据
    for (const [name, metadata] of data.parameterMetadata) {
      this.parameterMetadata.set(name, metadata);
    }

    // 恢复动画事件
    for (const [stateName, events] of data.animationEvents) {
      this.animationEvents.set(stateName, events);
    }

    // 恢复配置
    if (data.config) {
      this.config = data.config;
    }

    // 恢复当前状态
    if (data.currentState) {
      this.setCurrentState(data.currentState);
    }
  }

  /**
   * 解析条件表达式
   * @param expression 条件表达式
   * @returns 条件函数
   */
  private parseConditionExpression(expression: string): () => boolean {
    // 简化的条件表达式解析
    return () => {
      try {
        // 替换参数引用
        let processedExpression = expression;
        for (const [name, value] of this.parameters) {
          const regex = new RegExp(`\\b${name}\\b`, 'g');
          processedExpression = processedExpression.replace(regex, String(value));
        }

        // 使用Function构造器创建函数（注意：生产环境中应该使用更安全的解析器）
        return new Function(`return ${processedExpression}`)();
      } catch (error) {
        console.warn(`条件表达式解析失败: ${expression}`, error);
        return false;
      }
    };
  }
}

/**
 * 条件解析器
 */
class ConditionParser {
  /**
   * 解析条件表达式
   * @param expression 表达式
   * @param parameters 参数映射
   * @returns 解析结果
   */
  public parse(expression: string, parameters: Map<string, any>): boolean {
    // 简化实现
    try {
      let processedExpression = expression;
      for (const [name, value] of parameters) {
        const regex = new RegExp(`\\b${name}\\b`, 'g');
        processedExpression = processedExpression.replace(regex, String(value));
      }
      return new Function(`return ${processedExpression}`)();
    } catch {
      return false;
    }
  }
}

/**
 * 混合空间处理器
 */
class BlendSpaceProcessor {
  /**
   * 处理1D混合空间
   * @param config 混合空间配置
   * @param paramValue 参数值
   * @returns 混合结果
   */
  public process1D(config: BlendSpaceConfig, paramValue: number): any {
    // 实现1D混合空间处理逻辑
    return null;
  }

  /**
   * 处理2D混合空间
   * @param config 混合空间配置
   * @param paramValue 参数值
   * @returns 混合结果
   */
  public process2D(config: BlendSpaceConfig, paramValue: [number, number]): any {
    // 实现2D混合空间处理逻辑
    return null;
  }
}

/**
 * 转换曲线处理器
 */
class TransitionCurveProcessor {
  /**
   * 应用转换曲线
   * @param t 时间参数 (0-1)
   * @param curveType 曲线类型
   * @returns 处理后的值
   */
  public applyCurve(t: number, curveType: TransitionCurveType): number {
    switch (curveType) {
      case TransitionCurveType.LINEAR:
        return t;
      case TransitionCurveType.EASE_IN:
        return t * t;
      case TransitionCurveType.EASE_OUT:
        return 1 - (1 - t) * (1 - t);
      case TransitionCurveType.EASE_IN_OUT:
        return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
      case TransitionCurveType.SMOOTH_STEP:
        return t * t * (3 - 2 * t);
      default:
        return t;
    }
  }
}
